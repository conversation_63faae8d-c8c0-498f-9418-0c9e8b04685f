#if NV_FEATURE_CUPTI_ENABLE_PM_TRIGGER_API
  cuptiIssueTriggerWithPayload
  cuptiIssueTriggerToStartMetricsCollection
  cuptiIssueTriggerToStopMetricsCollection
#endif
cuptiActivityEnableDeviceGraph
cuptiActivityEnableDriverApi
cuptiActivityEnableRuntimeApi
#if NV_FEATURE(CUDA_DX12_SCG)
cuptiEnableCigMode
#endif
#if NV_FEATURE(PERFWORKS_HES_TRACE) && NV_FEATURE(GPU_ARCH_GB100)
cuptiActivityEnableHWTrace
#endif
#if NV_FEATURE_CUPTI_ENABLE_PROFILER_HOST_APIS
  cuptiProfilerHostInitialize
  cuptiProfilerHostDeinitialize
  cuptiProfilerHostGetSupportedChips
  cuptiProfilerHostGetBaseMetrics
  cuptiProfilerHostGetSubMetrics
  cuptiProfilerHostGetMetricProperties
  cuptiProfilerHostGetRangeName
  cuptiProfilerHostEvaluateToGpuValues
  cuptiProfilerHostConfigAddMetrics
  cuptiProfilerHostGetConfigImageSize
  cuptiProfilerHostGetConfigImage
  cuptiProfilerHostGetNumOfPasses
  cuptiProfilerHostGetMaxNumHardwareMetricsPerPass
#endif