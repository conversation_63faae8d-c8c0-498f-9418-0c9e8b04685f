import re

def extract_similar_numbers_to_2622561():
    """
    从report1.txt中提取与2622561类似长度的数字
    """
    reference_number = "2622561"
    reference_length = len(reference_number)
    
    print(f"参考数字: {reference_number}, 长度: {reference_length}")
    
    try:
        with open("report1.txt", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        similar_numbers = []
        exact_length_numbers = []
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if line and not line.startswith('Total'):
                # 查找所有数字，跳过行号
                numbers = re.findall(r'\b\d+\b', line)
                for num in numbers:
                    # 跳过行号（通常是1-3位数字），只考虑长数字
                    if len(num) >= 6:
                        # 检查长度是否完全相同
                        if len(num) == reference_length:
                            exact_length_numbers.append(num)
                        
                        # 检查长度是否相似（允许±1的长度差异）
                        if abs(len(num) - reference_length) <= 1:
                            similar_numbers.append(num)
        
        # 去重并保持顺序
        unique_similar_numbers = []
        for num in similar_numbers:
            if num not in unique_similar_numbers:
                unique_similar_numbers.append(num)
        
        unique_exact_numbers = []
        for num in exact_length_numbers:
            if num not in unique_exact_numbers:
                unique_exact_numbers.append(num)
        
        print(f"\n找到的所有6位以上数字: {len(unique_similar_numbers)} 个")
        print(f"7位数字: {len(unique_exact_numbers)} 个")
        
        # 显示前20个7位数字
        print(f"\n前20个7位数字:")
        for i, num in enumerate(unique_exact_numbers[:20]):
            print(f"{i+1}. {num}")
        
        if len(unique_exact_numbers) > 20:
            print(f"... 还有 {len(unique_exact_numbers) - 20} 个")
        
        # 按前缀分组
        print(f"\n按前缀分组:")
        prefixes = {}
        for num in unique_exact_numbers:
            prefix = num[:2]
            if prefix not in prefixes:
                prefixes[prefix] = []
            prefixes[prefix].append(num)
        
        for prefix in sorted(prefixes.keys()):
            count = len(prefixes[prefix])
            examples = prefixes[prefix][:3]
            print(f"{prefix}xxxxx: {count} 个 (例: {', '.join(examples)})")
        
        # 查找包含2622561的位置
        if reference_number in unique_exact_numbers:
            index = unique_exact_numbers.index(reference_number)
            print(f"\n参考数字 {reference_number} 在列表中的位置: 第 {index + 1} 个")
        
        # 查找与参考数字最接近的数字
        ref_int = int(reference_number)
        int_numbers = [int(num) for num in unique_exact_numbers]
        closest_numbers = sorted(int_numbers, key=lambda x: abs(x - ref_int))[:10]
        
        print(f"\n与 {reference_number} 最接近的10个数字:")
        for i, num in enumerate(closest_numbers):
            diff = abs(num - ref_int)
            print(f"{i+1}. {num} (差值: {diff})")
        
        # 生成文件
        with open('numbers_like_2622561.txt', 'w', encoding='utf-8') as f:
            for i, num in enumerate(unique_exact_numbers, 1):
                f.write(f"{i}. {num}\n")
        print(f"\n已生成数字列表文件: numbers_like_2622561.txt")
        
        # 生成Python列表格式
        with open('python_list_2622561.txt', 'w', encoding='utf-8') as f:
            f.write(str(unique_exact_numbers))
        print(f"已生成Python列表格式文件: python_list_2622561.txt")
        
        # 生成一行式列表
        with open('one_line_2622561.txt', 'w', encoding='utf-8') as f:
            f.write(', '.join(unique_exact_numbers))
        print(f"已生成一行式列表文件: one_line_2622561.txt")
        
        print(f"\n完整的7位数字列表:")
        print(unique_exact_numbers)
        
        return unique_exact_numbers
        
    except FileNotFoundError:
        print("错误: 找不到 report1.txt 文件")
        return []
    except Exception as e:
        print(f"发生错误: {e}")
        return []

if __name__ == "__main__":
    print("=" * 60)
    print("从 report1.txt 中提取与 2622561 类似长度的数字")
    print("=" * 60)
    
    numbers = extract_similar_numbers_to_2622561()
    
    print(f"\n=== 文件生成完成 ===")
    print(f"1. numbers_like_2622561.txt - 带编号的列表")
    print(f"2. python_list_2622561.txt - Python列表格式")
    print(f"3. one_line_2622561.txt - 一行式，逗号分隔")
    print(f"\n总共找到 {len(numbers)} 个与 2622561 长度相同的7位数字")
