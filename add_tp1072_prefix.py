def add_tp1072_prefix():
    """
    为数字列表添加TP1072_T前缀
    """
    # 原始数字列表
    numbers = [1887453, 1998582, 1887484, 2575487, 2630887, 2806638, 3022207, 3193517, 3269121, 3311698, 3374932, 3423837, 3526161, 3533710, 3556170, 3601648, 3882578, 3954217, 5010260, 5246414, 5364926, 2684438, 2735641, 2753438, 2769957, 2772106, 2801678, 2833769, 2907082, 3157015, 3195504, 3270464, 3408932, 4038584, 5102734, 3146235, 1887429, 2943175, 2622515, 2622561, 2622563, 3050723, 3448379, 2795007]
    
    # 添加前缀
    prefixed_numbers = [f"TP1072_T{num}" for num in numbers]
    
    print("原始数字列表:")
    print(numbers)
    print(f"\n原始数字总数: {len(numbers)}")
    
    print("\n带TP1072_T前缀的数字列表:")
    print(prefixed_numbers)
    
    print(f"\n前20个带前缀的数字:")
    for i, num in enumerate(prefixed_numbers[:20]):
        print(f"{i+1}. {num}")
    
    if len(prefixed_numbers) > 20:
        print(f"... 还有 {len(prefixed_numbers) - 20} 个")
    
    # 生成带编号的文件
    with open('tp1072_prefixed_numbers.txt', 'w', encoding='utf-8') as f:
        for i, num in enumerate(prefixed_numbers, 1):
            f.write(f"{i}. {num}\n")
    print(f"\n已生成带编号的列表文件: tp1072_prefixed_numbers.txt")
    
    # 生成Python列表格式
    with open('tp1072_python_list.txt', 'w', encoding='utf-8') as f:
        f.write(str(prefixed_numbers))
    print(f"已生成Python列表格式文件: tp1072_python_list.txt")
    
    # 生成一行式列表（逗号分隔）
    with open('tp1072_one_line.txt', 'w', encoding='utf-8') as f:
        f.write(', '.join(prefixed_numbers))
    print(f"已生成一行式列表文件: tp1072_one_line.txt")
    
    # 生成带双引号的列表
    quoted_prefixed = [f'"{num}"' for num in prefixed_numbers]
    with open('tp1072_quoted_list.txt', 'w', encoding='utf-8') as f:
        f.write(', '.join(quoted_prefixed))
    print(f"已生成带双引号的一行式列表文件: tp1072_quoted_list.txt")
    
    print(f"\n复制用的一行式格式:")
    print(', '.join(prefixed_numbers))
    
    print(f"\n复制用的带双引号格式:")
    print(', '.join(quoted_prefixed))
    
    return prefixed_numbers

if __name__ == "__main__":
    print("=" * 60)
    print("为数字列表添加 TP1072_T 前缀")
    print("=" * 60)
    
    result = add_tp1072_prefix()
    
    print(f"\n=== 文件生成完成 ===")
    print(f"1. tp1072_prefixed_numbers.txt - 带编号的列表")
    print(f"2. tp1072_python_list.txt - Python列表格式")
    print(f"3. tp1072_one_line.txt - 一行式，逗号分隔")
    print(f"4. tp1072_quoted_list.txt - 带双引号，逗号分隔")
    print(f"\n总共处理了 {len(result)} 个数字")
