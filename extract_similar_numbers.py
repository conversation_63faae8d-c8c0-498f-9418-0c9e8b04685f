import re

def extract_similar_length_numbers(file_path, reference_number="2957998"):
    """
    从文件中提取与参考数字长度相似的数字
    """
    reference_length = len(reference_number)
    print(f"参考数字: {reference_number}, 长度: {reference_length}")
    
    similar_numbers = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正则表达式查找所有数字
        # 匹配连续的数字
        numbers = re.findall(r'\b\d+\b', content)
        
        print(f"文件中找到的所有数字: {numbers}")
        
        # 筛选长度相似的数字（允许±1的长度差异）
        for number in numbers:
            if abs(len(number) - reference_length) <= 1:  # 允许长度差1
                similar_numbers.append(number)
        
        # 去重并保持顺序
        unique_similar_numbers = []
        for num in similar_numbers:
            if num not in unique_similar_numbers:
                unique_similar_numbers.append(num)
        
        print(f"\n长度相似的数字 (长度 {reference_length-1} 到 {reference_length+1}):")
        for i, num in enumerate(unique_similar_numbers):
            print(f"{i+1}. {num} (长度: {len(num)})")
        
        return unique_similar_numbers
        
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 未找到")
        return []
    except Exception as e:
        print(f"发生错误: {e}")
        return []

def extract_exact_length_numbers(file_path, reference_number="2957998"):
    """
    从文件中提取与参考数字完全相同长度的数字
    """
    reference_length = len(reference_number)
    print(f"参考数字: {reference_number}, 长度: {reference_length}")
    
    exact_length_numbers = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 使用正则表达式查找指定长度的数字
        pattern = r'\b\d{' + str(reference_length) + r'}\b'
        numbers = re.findall(pattern, content)
        
        print(f"文件中找到的{reference_length}位数字: {numbers}")
        
        # 去重并保持顺序
        unique_numbers = []
        for num in numbers:
            if num not in unique_numbers:
                unique_numbers.append(num)
        
        print(f"\n完全相同长度的数字 (长度 {reference_length}):")
        for i, num in enumerate(unique_numbers):
            print(f"{i+1}. {num}")
        
        return unique_numbers
        
    except FileNotFoundError:
        print(f"错误: 文件 {file_path} 未找到")
        return []
    except Exception as e:
        print(f"发生错误: {e}")
        return []

if __name__ == "__main__":
    file_path = "test_data.txt"
    reference_number = "2957998"  # 你选中的数字
    
    print("=" * 50)
    print("提取长度相似的数字 (允许±1长度差异):")
    print("=" * 50)
    similar_numbers = extract_similar_length_numbers(file_path, reference_number)
    
    print("\n" + "=" * 50)
    print("提取完全相同长度的数字:")
    print("=" * 50)
    exact_numbers = extract_exact_length_numbers(file_path, reference_number)
    
    print(f"\n总结:")
    print(f"长度相似的数字: {similar_numbers}")
    print(f"完全相同长度的数字: {exact_numbers}")
