import pandas as pd

def extract_failed_template_ids(excel_file):
    """
    从vGPU.xlsx中提取失败案例的template id
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 显示列名以便调试
        print("Excel文件的列名:")
        print(df.columns.tolist())
        print("\n前几行数据:")
        print(df.head())
        
        # 查找包含'fail'或'FAIL'的行
        # 这里假设状态列可能叫'Status', 'Result', 'Case Status'等
        failed_cases = []
        template_ids = []
        
        # 尝试不同的可能列名
        status_columns = ['Status', 'Result', 'Case Status', 'Test Result', 'status', 'result']
        template_columns = ['Template ID', 'template id', 'Template_ID', 'TemplateID', 'ID']
        
        status_col = None
        template_col = None
        
        # 查找状态列
        for col in df.columns:
            if any(status_name.lower() in col.lower() for status_name in status_columns):
                status_col = col
                break
        
        # 查找template id列
        for col in df.columns:
            if any(template_name.lower() in col.lower() for template_name in template_columns):
                template_col = col
                break
        
        if status_col is None:
            print("未找到状态列，尝试在所有列中查找包含'fail'的行...")
            # 在所有列中查找包含fail的行
            for index, row in df.iterrows():
                row_str = ' '.join(str(val).lower() for val in row.values if pd.notna(val))
                if 'fail' in row_str:
                    failed_cases.append(index)
                    if template_col:
                        template_ids.append(row[template_col])
                    else:
                        # 如果没有找到template列，尝试第一列
                        template_ids.append(row.iloc[0])
        else:
            print(f"找到状态列: {status_col}")
            if template_col:
                print(f"找到Template ID列: {template_col}")
            
            # 查找失败的案例
            for index, row in df.iterrows():
                status = str(row[status_col]).lower()
                if 'fail' in status:
                    failed_cases.append(index)
                    if template_col:
                        template_ids.append(row[template_col])
                    else:
                        template_ids.append(row.iloc[0])
        
        print(f"\n找到 {len(failed_cases)} 个失败案例")
        print("失败案例的Template IDs:")
        for i, template_id in enumerate(template_ids):
            print(f"{i+1}. {template_id}")
        
        return template_ids
        
    except FileNotFoundError:
        print(f"错误: 文件 {excel_file} 未找到")
        return []
    except Exception as e:
        print(f"发生错误: {e}")
        return []

if __name__ == "__main__":
    excel_file = "vGPU.xlsx"
    failed_template_ids = extract_failed_template_ids(excel_file)
    
    print(f"\n失败案例的Template ID列表:")
    print(failed_template_ids)
