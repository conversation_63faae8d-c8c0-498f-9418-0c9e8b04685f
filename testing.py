
# -*- coding: utf-8 -*-

s1='a100d,a100d,'
print(s1.split(','))
a = ['1', '1', '1']
print(len(a))
print(len(set(a)))
'''
s2 = '00000:sd:3:0'
print(s2.lstrip('0'))
fail_list = []
with open('report1.txt', 'r') as f:
    for line in f.readlines():
        if 'P1072' in line and 'FAILED' in line:
            print line

            fail_list.append(line.split(',')[0])
print(len(fail_list))
print(fail_list)
import xlrd
workbook = xlrd.open_workbook('vgpu_info2.xls')
print(workbook)
sheet = workbook.sheet_by_index(0)
print(sheet.row_len(1))
print(sheet.row_values(0))
print(sheet.row_values(2))
print(sheet.col_values(6))

import json
def read_excel(file_path):
    workbook = xlrd.open_workbook(file_path)
    sheet = workbook.sheet_by_index(0)
    data = []
    for row in range(1, sheet.nrows):
        row_data = {}
        for col in range(sheet.ncols):
            row_data[sheet.cell_value(0, col)] = sheet.cell_value(row, col)
        data.append(row_data)

    return data

data1 = read_excel('vgpu_info2.xls')
print(data1)
json_data = json.dumps(data1, indent=6)
print(json_data)

b = ['4,194,304', '4,194,304', '0', '8,388,608', '', '8,389,558']
result = ' '.join(b)
print(result)
dss = '00000009'
print(dss[-4:])
print("a. qemu-img create -f qcow2 myvm_disk.qcow2 350G \n b. download tot ISO file from https://urm.nvidia.com/artifactory/sw-dgx-platform-generic-local/ghvirt-iso/\n c. download rom file(efi-e1000.rom) from  https://github.com/qemu/qemu/blob/master/pc-bios/efi-e1000.rom \nd. select you used base os version, such as BaseOS-Grace-GHVIRT-6.0.0-2024-02-11-20-15-18.iso \ne. On host machine, use — 'ssh -p 5558 user@127.0.0.1' to login guest")


print(
        """
        a. qemu-img create -f qcow2 myvm_disk.qcow2 350G \n

        b. download tot ISO file from https://urm.nvidia.com/artifactory/sw-dgx-platform-generic-local/ghvirt-iso/ \n

        c. download rom file(efi-e1000.rom) from  https://github.com/qemu/qemu/blob/master/pc-bios/efi-e1000.rom \n
        d. select you used base os version, such as BaseOS-Grace-GHVIRT-6.0.0-2024-02-11-20-15-18.iso \n
        e. On host machine, use — 'ssh -p 5558 user@127.0.0.1' to login guest
        """
        )

'''
used_bus = ['0000', '81', '02.2']
sd = "        <address type='pci' domain='0x0000' bus='0x{}' slot='0x{}' function='0x{}'/>".format(used_bus[1], used_bus[2].split('.')[0], used_bus[2].split('.')[1])

fail_list = []
with open('report1.txt', 'r') as f:
    for line in f.readlines():
        print(line)
        if len(line) == 8 and line not in fail_list:
            fail_list.append(line.strip('\n'))

print(fail_list)
fail1_list = ['P1072_T' + i for i in fail_list]
print(set(fail1_list))

find1 = 0
file_list = []
with open('file1', 'r') as f:
    print(f.readline)
    for index, line in enumerate(f.readlines()):
        file_list.append(line.strip('\n'))
print('00000000000000000000000000')
print(find1)
print('aaaaaaaaaaaaaaa')
print(file_list)
find2 = 0
for index, line1 in enumerate(file_list):
    if '3       Ready' in line1 and '(4-10)' in file_list[index + 1] and '(11-31)' in file_list[index + 2]:
        find1 += 1
    if '3       Ready' in line1 and '(4-6)' in file_list[index + 1] and '7' in file_list[index + 2] and '(8-10)' in file_list[index + 3] and '(11-31)' in file_list[index +4]:
        find2 += 1
print(find1)
print(find2)
import re
list11 = tuple(int(x.split('=')[1].strip()) for x in "$1 = {x = 70000, y = 1, z = 1}".replace('$1 = {', '').replace('}', '').split(','))
print(list11)
text = '1 Active 0x3fffffffff (70000,1,1) (1024,1,1)'
pattern = r'\((\d+),(\d+),(\d+)\)'
matches = re.findall(pattern, text)
result = [tuple(map(int, match)) for match in matches]
print(result)

def get_cupti_api(api_file, cupti_api):
    in_dx12_scg_block = False
    cupti_api_list = []
    with open(api_file, 'r') as f:
        for line in f.readlines():
            if f'#if {cupti_api}' in line:
                in_dx12_scg_block = True
                continue
            if in_dx12_scg_block and '#endif' in line:
                in_dx12_scg_block = False

                # Extract API if within the block
            if in_dx12_scg_block and line and not line.startswith('#'):
                api_name = line.strip()
                cupti_api_list.append(api_name)
    return cupti_api_list
api_list = get_cupti_api('result.txt', 'NV_FEATURE_CUPTI_ENABLE_PROFILER_HOST_APIS')
print (api_list)
